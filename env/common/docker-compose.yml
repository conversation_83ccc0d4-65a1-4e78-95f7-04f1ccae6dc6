version: '2'
services:

  php-fpm:
    build:
      context: .
      dockerfile: config/php-fpm/Dockerfile

    #:7.4-0.0.1, 8.1-0.0.0

    restart: on-failure:5
    volumes:
      - ./config/php-fpm/sourceguardian.ini:/etc/php.d/15-sourceguardian.ini
      - ./config/php-fpm/opcache.blacklist:/etc/php.d/opcache.blacklist
      - ./config/php-fpm/opcache.ini:/etc/php.d/zzy-opcache.ini
      - ./config/php-fpm/magento.ini:/etc/php.d/zzz-magento.ini
      - ./config/php-fpm/www.conf:/etc/php-fpm.d/www.conf
      - ../..:/var/www
      - ./config/php-fpm/xdebug.ini:/etc/php.d/xdebug.ini
    environment:
      - XDEBUG_ENABLED=true
#    environment:
#      - SMTP_SERVICE_ENABLED=true
#      - SMTP_POSTFIX_ENABLED=true
#      - NEW_RELIC_ENABLED=$NEW_RELIC_ENABLED
#      - NEW_RELIC_LICENSE_KEY=$NEW_RELIC_LICENSE_KEY
#      - NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  nginx:
    image: balanceinternet/docker-nginx-web:1.15-1.0.1
    restart: on-failure:5
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/magento.conf:/etc/nginx/magento.conf
      - ./config/nginx/domain_mapping.conf:/etc/nginx/conf.d/0_domain_mapping.conf
      - ../..:/var/www
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  proxy:
    image: balanceinternet/docker-nginx-web:1.15-1.0.1
    restart: on-failure:5
    volumes:
      - ./config/nginx/domain_mapping.conf:/etc/nginx/conf.d/0_domain_mapping.conf
      - ./config/proxy/proxy.conf:/etc/nginx/proxy.conf
      - ./config/proxy/nginx.conf:/etc/nginx/nginx.conf
      - ./config/proxy/blacklist.conf:/etc/nginx/conf.d/0_blacklist.conf
      - ../..:/var/www
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  varnish:
    image: balanceinternet/docker-varnish:6.5-2.0.0
    restart: on-failure:5
    environment:
      - VARNISH_STORAGE_AMOUNT=2G
      - VARNISH_TRANSIENT_STORAGE_AMOUNT=2G
      - VARNISH_RESP_HDR_LEN=65536
      - VARNISH_RESP_SIZE=98304
    volumes:
      - ./config/varnish/magento.vcl:/etc/varnish/config/custom.vcl
      - ../..:/var/www
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  logrotate:
    image: blacklabelops/logrotate
    restart: always
    volumes:
      - ./config/logrotate/logrotate.conf:/etc/logrotate.conf
    logging:
      driver: "json-file"
      options:
        max-size: "100M"
        max-file: "3"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  mysql:
    image: mysql:8.0
    restart: on-failure:5
    environment:
      - MYSQL_ROOT_PASSWORD=root
    command: --default-authentication-plugin=mysql_native_password
    logging:
      driver: "json-file"
      options:
        max-size: "100M"
        max-file: "3"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  redis:
    image: redis:6.2
    restart: on-failure:5
    logging:
      driver: "json-file"
      options:
        max-size: "100M"
        max-file: "3"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  # mailhog:
  #   image: mailhog/mailhog:latest
  #   restart: on-failure:5
  #   environment:
  #     - MH_STORAGE=maildir
  #     - MH_MAILDIR_PATH=/mnt/storage
  #   ports:
  #     - 8025:8025
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "100M"
  #       max-file: "3"
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #     nofile:
  #       soft: 65536
  #       hard: 65536

  # elasticsearch:
  #   image: balanceinternet/docker-elasticsearch:7.16-0.0.0
  #   restart: on-failure:5
  #   environment:
  #     - discovery.type=single-node
  #     - bootstrap.memory_lock=true
  #     - "ES_JAVA_OPTS=-Xms1024m -Xmx1024m -Des.index.max_number_of_shards=1 -Des.index.max_result_window=1"
  #     - node.max_local_storage_nodes=2
  #     - discovery.seed_hosts=[]
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "100M"
  #       max-file: "3"
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #     nofile:
  #       soft: 65536
  #       hard: 65536
  #   mem_limit: 2G

  # sftp:
  #   # container_name: sftp
  #   image: balanceinternet/docker-sftp:6.6-1.0.3
  #   restart: on-failure:5
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "100M"
  #       max-file: "3"
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #     nofile:
  #       soft: 65536
  #       hard: 65536

  rabbitmq:
    restart: on-failure:5
    image: rabbitmq:3-management
    logging:
      driver: "json-file"
      options:
        max-size: "100M"
        max-file: "3"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  postfix:
    image: balanceinternet/docker-postfix:1.0-1.0.0
    restart: on-failure:5
    environment:
      - SMTP_SERVER=email-smtp.us-west-2.amazonaws.com
      - SMTP_USERNAME=$SMTP_USERNAME
      - SMTP_PASSWORD=$SMTP_PASSWORD
      - SMTP_PORT=587
      - SMTP_USE_TLS=yes
    logging:
      driver: "json-file"
      options:
        max-size: "100M"
        max-file: "3"
