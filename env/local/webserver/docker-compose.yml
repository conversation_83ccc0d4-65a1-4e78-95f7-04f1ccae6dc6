version: '2'
services:

  php-fpm:
    extends:
      file: ../../common/docker-compose.yml
      service: php-fpm
    volumes:
      #- ./config/php-fpm/xdebug.ini:/etc/php.d/zzz-xdebug.ini
      - ./volumes/shared:/mnt/web:delegated
      - ./volumes/composer:/home/<USER>/.composer:delegated
      - ../../..:/var/www:delegated
    #environment:
      #- XDEBUG_ENABLED=true

  nginx:
    extends:
      file: ../../common/docker-compose.yml
      service: nginx
    volumes:
      - ./config/nginx/webserver.conf:/etc/nginx/conf.d/webserver.conf
      - ./volumes/shared/log/nginx:/var/www/log/nginx:delegated
      - ../../..:/var/www:delegated

  ssl-proxy:
    image: nginx:1.15
    volumes:
      - ./config/ssl-proxy/ssl-proxy.localhost.conf:/etc/nginx/nginx.conf
      - certificates:/etc/ssl/
      - ./volumes/shared:/mnt/web:delegated
    depends_on:
      - varnish
    ports:
      - 80:80
      - 443:443

  openssl:
    build: config/openssl
    volumes:
      - certificates:/certificates

  varnish:
    extends:
      file: ../../common/docker-compose.yml
      service: varnish
    volumes:
      - ./volumes/shared:/mnt/web:delegated

  logrotate:
    extends:
      file: ../../common/docker-compose.yml
      service: logrotate
    volumes:
      - ./volumes/shared/log:/log

  redis:
    extends:
      file: ../../common/docker-compose.yml
      service: redis

  mysql:
    extends:
      file: ../../common/docker-compose.yml
      service: mysql
    volumes:
      - ./volumes/mysql:/var/lib/mysql:delegated
      - ./config/mysql/import:/docker-entrypoint-initdb.d:delegated
      - ./config/mysql/magento.cnf:/etc/mysql/conf.d/magento.cnf:delegated
    ports:
      - 3309:3306

  # elasticsearch:
  #   extends:
  #     file: ../../common/docker-compose.yml
  #     service: elasticsearch
  #   volumes:
  #     - ./volumes/elasticsearch:/usr/share/elasticsearch/data:delegated

  # mailhog:
  #   extends:
  #     file: ../../common/docker-compose.yml
  #     service: mailhog
  #   volumes:
  #     - ./volumes/email:/mnt/storage
  #   environment:
  #     - MH_STORAGE=maildir
  #     - MH_MAILDIR_PATH=/mnt/storage
  #   ports:
  #     - 8027:8025

  # sftp:
  #   extends:
  #     file: ../../common/docker-compose.yml
  #     service: sftp
  #   volumes:
  #     - ./volumes/sftp:/var/sftp/web
  #   environment:
  #     # test:12345
  #     - SFTP_USER_PASS=test:$$6$$b4rjYhDZ$$WgaXVwwfkR7bL5/u7mRAVM31hR/T3hILHJUUmij/amZDI4SI3f19/KG8Zvm.QCrDyHrziSd3HhOywbwZAfFGv0
  #   ports:
  #     - "2222:2222"

  rabbitmq:
    extends:
      file: ../../common/docker-compose.yml
      service: rabbitmq
    ports:
      - 15672:15672

# Uncomment, if blackfire is needed
#  blackfire:
#    extends:
#      file: ../../common/docker-compose.yml
#      service: blackfire
#    environment:
#      - BLACKFIRE_SERVER_ID=$BLACKFIRE_SERVER_ID
#      - BLACKFIRE_SERVER_TOKEN=$BLACKFIRE_SERVER_TOKEN

volumes:
  certificates:
    driver: local
